/* 微软雅黑Light字体 */

:root {
  --base-font-size: calc(12px + 0.3vw); /* 优化笔记本缩放比例 */
  --min-font-size: 12px;
  --laptop-scale: 0.85; /* 笔记本专用缩放系数 */
}

*,
*::before,
*::after {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: 'Source Han Sans CN', '思源黑体', sans-serif;
}

body {
  font-family: 'Source Han Sans CN', '思源黑体', sans-serif;
  background: #000;
  color: #fff;
  font-size: clamp(var(--min-font-size), var(--base-font-size), 20px); /* 等比例缩放 */
}

a {
  text-decoration: none;
  color: #fff;
}

/* 出版物标题样式 */
.publication-title {
  color: rgb(98, 0, 110);
  font-size: calc(24px + 0.5vw);
}

/* 报告链接响应式字体 */
.report-link {
  font-size: calc(20px + 0.5vw);
  color: #ffffff;
}

/* 移动端字体调整 */
@media (max-width: 600px) {
  .publication-title {
    font-size: calc(20px + 0.5vw);
  }
  .report-link {
    font-size: calc(18px + 0.5vw);
  }
}


ul {
  list-style: none;
}

/* Header/Navbar */
.main-header {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 3;
  display: flex;
  justify-content: space-between;
  align-items: center;
  text-transform: uppercase;
  height: 100px;
  padding: 0 30px;
  transition: all 0.3s ease;
}

/* Logo - 响应式样式 */
.logo {
  width: clamp(180px, 15vw, 280px); /* 响应式缩放 */
  height: auto;
  transition: all 0.3s ease;
}

/* 移动端字体调整 */
@media (max-width: 768px) {
  /* 调整logo样式 */
  .fixed-logo,
  .fixed-logo img,
  .logo {
    width: 100px !important;
    min-width: 80px !important;
    max-width: 100px !important;
    height: auto !important;
  }

  /* h2字体放大2倍 */
  section .section-inner h2 {
    font-size: clamp(1.8rem, 6vw, 4rem) !important;
    line-height: 1.5 !important;
    font-weight: 400 !important;
  }

  /* 底部字体缩小一倍 */
  footer ul li {
    font-size: 0.7rem !important;
  }
}

.section-inner-center h3 {
  font-size: clamp(
    1.2rem,
    calc(1.2rem + 1vw),
    2.5rem
  );
  color: #f36f04fb;
  transition: font-size 0.3s ease;
}

/* 移动端重置缩放基准 */
@media (max-width: 768px) {
  :root {
    --scale-factor: calc(100vw / 768 * 0.5); /* 进一步缩小移动端缩放比例 */
    font-size: calc(12px + 0.5vw); /* 移动端字体等比例缩放 */
  }
  
  .logo {
    min-width: 30px;
  }
  
  .section-inner-center h3 {
    min-font-size: 0.8rem;
  }
}

/* 图片基础样式 */
.logo img {
  display: block;
  width: 100%;
  height: auto;
  max-width: 100%; /* 确保图片不会超出容器 */
}

/* 移动端图片特殊处理 */
@media (max-width: 600px) {
  .logo img {
    max-width: 100% !important; /* 确保覆盖 */
  }
}

/* Desktop Menu */
.desktop-main-menu {
  margin-right: 50px;
}

.desktop-main-menu ul {
  display: flex;
}

/* 桌面菜单字体优化 - 响应式不换行 */
header.main-header nav.desktop-main-menu > ul > li {
  position: relative;
  margin-right: 20px;
  padding-bottom: 2px;
  font-size: clamp(14px, 1.0vw, 24px) !important; /* 平滑缩放 */
  font-family: 'Source Han Sans CN', '思源黑体', sans-serif !important;
  font-weight: normal !important;
  color: #fff !important;
  text-transform: uppercase !important;
  white-space: nowrap !important; /* 禁止换行 */
  transition: font-size 0.3s ease;
}

/* 确保菜单项总宽度不超过视口 */
header.main-header nav.desktop-main-menu > ul {
  display: flex;
  flex-wrap: nowrap;
  overflow-x: auto; /* 小屏时可横向滚动 */
  scrollbar-width: none; /* 隐藏滚动条 */
}

header.main-header nav.desktop-main-menu > ul::-webkit-scrollbar {
  display: none;
}

/* 移动端菜单优化 */
@media (max-width: 960px) {
  header.main-header nav.desktop-main-menu > ul > li {
    font-size: 14px !important;
    margin-right: 12px;
  }
}


/* Menu item bottom border */
.desktop-main-menu ul li a::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 1px;
  background: #fff;
  transform: scaleX(0);
  transition: transform 0.6s cubic-bezier(0.19, 1, 0.22, 1);
  transform-origin: right center;
}

.desktop-main-menu ul li a:hover::after {
  transform: scaleX(1);
  transform-origin: left center;
  transition-duration: 0.4s;
}

/* Sections */
section {
  position: relative;
  height: 100vh;
  background-position: center center;
  background-repeat: no-repeat;
  background-size: cover;
  text-transform: uppercase;
}

.section-inner {
  position: absolute;
  bottom: 15vh;
  left: 5vw; /* 左移5vw */
  max-width: 60vw;
  width: 80%;
}

.section-inner h4 {
  font-size: clamp(0.79rem, calc(0.82rem + 0.45vw), 1.35rem); /* 微调最小值 */
  margin-bottom: 1vh;
  font-weight: 300;
  animation: fadeInUp 0.5s ease-in-out;
  line-height: 1.4;
  transition: font-size 0.3s ease;
}

/* 移动端字体强制覆盖 */
@media (max-width: 768px) {
  .section-inner h2 {
    font-size: 0.8rem !important;
    line-height: 1.2;
  }
  .section-inner h4 {
    font-size: min(calc(0.8rem + 1vw), 1.5rem);
  }
}

/* 电脑端h2优化 */
.section-inner h2 {
  font-size: clamp(1.8rem, 1.5vw + 1rem, 3rem); /* 更精确的视口单位计算 */
  font-weight: 300;
  margin-bottom: 2vh;
  animation: fadeInUp 0.5s ease-in-out 0.2s;
  animation-fill-mode: both;
  line-height: 1.3;
  letter-spacing: 0.5px;
  transition: font-size 0.3s ease;
  white-space: nowrap; /* 防止文字换行 */
}

/* 移动端保持原有样式 */
@media (max-width: 768px) {
  .section-inner h2 {
    font-size: clamp(1.5rem, 4vw, 2rem);
    white-space: normal; /* 小屏允许换行 */
  }
}

.section-inner a {
  animation: fadeInUp 0.5s ease-in-out 0.4s;
  animation-fill-mode: both; // Stop from showing at start
}

/* Background images */
.section-a {
  background-image: url('../img/home.jpg');
}
.section-b {
  background-image: url('../img/antenna.jpg');
}
.section-c {
  background-image: url('../img/antenna2.jpg');
}
.section-d {
  background-image: url('../img/earth2.jpg');
}
.section-e {
  background-image: url('../img/security.jpg');
}
.section-f {
  background-image: url('../img/secure.jpg');
}

.btn {
  position: relative;
  display: inline-block;
  cursor: pointer;
  text-align: center;
  min-width: 130px;
  padding: 15px 50px;
  margin-top: 10px;
  border: 2px solid #fff;
  text-transform: uppercase;
  font-weight: bold;
  overflow: hidden;
  z-index: 2;
  font-size: clamp(0.9rem, calc(0.9rem + 0.3vw), 1.2rem);
}

.btn:hover span {
  color: #000;
}

.btn .hover {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: #fff;
  color: #000;
  z-index: -1;
  transform: translateY(100%);
  transition: transform 0.6s cubic-bezier(0.19, 1, 0.22, 1);
}

.btn:hover .hover {
  transform: translateY(0);
}

.scroll-arrow {
  position: absolute;
  bottom: 50px;
  left: calc(50% - 20px); /* 左移10px */
  transform: translateX(-50%);
  animation: fadeBounce 5s infinite;
}

/* Footer */
footer {
  position: relative;
  padding: 55px 0;
}

footer ul {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
}

footer ul li {
  margin-right: 30px;
  color: #aaa;
  text-transform: uppercase;
  font-size: clamp(13px, 0.8vw + 10px, 16px); /* 电脑端优化 */
  line-height: 2.5;
}

@media (max-width: 1440px) {
  footer ul li {
    font-size: clamp(10px, 0.6vw + 8px, 12px); /* 笔记本优化 */
  }
}

@media (min-width: 1920px) {
  footer ul li {
    font-size: 16px; /* 大屏固定尺寸 */
  }
}

footer ul li a {
  font-size: inherit;
}

footer ul li a {
  color: #fff;
  transition: color 0.6s;
}

footer ul li a:hover {
  color: #aaa;
}

/* Hamburger Menu */
.hamburger {
  position: fixed;
  top: 40px;
  right: 20px;
  z-index: 10;
  cursor: pointer;
  width: 20px;
  height: 20px;
  background: none;
  border: none;
}

.hamburger-top,
.hamburger-middle,
.hamburger-bottom {
  position: absolute;
  width: 20px;
  height: 2px;
  top: 0;
  left: 0;
  background: #fff;
  transition: all 0.5s;
}

.hamburger-middle {
  transform: translateY(5px);
}

.hamburger-bottom {
  transform: translateY(10px);
}

/* Transition hamburger to X when open */
.open {
  transform: rotate(90deg);
}

.open .hamburger-top {
  transform: rotate(45deg) translateY(6px) translateX(6px);
}

.open .hamburger-middle {
  display: none;
}

.open .hamburger-bottom {
  transform: rotate(-45deg) translateY(6px) translateX(-6px);
}

/* Overlay */
.overlay-show {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 3;
}

/* Stop body scroll */
.stop-scrolling {
  overflow: hidden;
}

/* Hide mobile main menu items */
.mobile-only {
  display: none;
}

/* Mobile menu */
.mobile-main-menu {
  position: fixed;
  top: 0;
  right: 0;
  width: 350px;
  height: 100%;
  background: #000;
  z-index: 4;
  display: flex;
  align-items: center;
  justify-content: center;
  transform: translateX(100%);
  transition: transform 0.6s cubic-bezier(0.19, 1, 0.22, 1);
}

/* Bring menu from right */
.show-menu {
  transform: translateX(0);
}

.mobile-main-menu ul {
  display: flex;
  flex-direction: column;
  align-items: end;
  justify-content: center;
  padding: 50px;
  width: 100%;
}

.mobile-main-menu ul li {
  margin-bottom: 20px;
  font-size: calc(18px + 0.25vw);
  text-transform: uppercase;
  font-family: 'Source Han Sans CN', '思源黑体', sans-serif;
  border-bottom: 1px #555 dotted;
  width: 100%;
  text-align: right;
  padding-bottom: 8px;
}

/* 产品子菜单样式 */
.mobile-main-menu .products-submenu {
  display: none;
  padding-right: 20px;
}

.mobile-main-menu .has-submenu:hover .products-submenu {
  display: block;
}

.mobile-main-menu .products-submenu li {
  border-bottom: none;
  font-size: calc(16px + 0.25vw);
}

.mobile-main-menu ul li a {
  color: #fff;
  transition: color 0.6s;
}

.mobile-main-menu ul li a:hover {
  color: #aaa;
}

/* 背景图 */
.bg-about {
  background-image: url('../img/about.jpg');
}

.bg-test {
  background-image: url('../img/test.jpg');
  position: relative;
}

.bg-test::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(2px);
  z-index: 0;
}

.section-animate.bg-test > * {
  position: relative;
  z-index: 1;
}

.bg-logo {
  background-image: url('../img/logo_big.png');
}


.bg-space-data{
  background-image: url('../img/space-data.jpg');
  background-size: cover;
  background-position: center;
  position: relative;
}

/*增加模糊*/
.bg-space-data::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(0.1px);
  z-index: 0;
}

.section-animate.bg-space-data > * {
  position: relative;
  z-index: 1;
}

.bg-spacesim {
  background-image: url('../img/secure.jpg');
}

.bg-satellite-sim {
  background-image: url('../img/satellite-sim.jpg');
}

.bg-firewall {
  background-image: url('../img/firewall.jpg');
}

.bg-publication {
  background-image: url('../img/publication.jpg');
}

.bg-contact {
  background-image: url('../img/contact.jpg');
}

.bg-weixin {
  background-image: url('../img/weixin.jpg');
  background-position: center right 150px; /* 右移150px */
  background-size: contain;
  background-repeat: no-repeat;
  opacity: 0.9;
  height: 100vh;
  width: 100%;
  position: relative;
  margin-right: 80px; /* 增加右边距至80px */
}


.section-animate {
  animation: fadeIn 0.1s ease-in-out;
}


/*about界面*/
.section-inner-top {
  position: absolute;
  top: 80%;
  left: 45%; /* 适当右移 */
  transform: translate(-50%, -50%);
  text-transform: uppercase;
  text-align: left;
  width: 80%;
}

.section-inner-top h5 {
  font-size: 65px;
  margin-bottom: 10px;
  animation: fadeInUp 0.5s ease-in-out;
}
.section-inner-top h3 {
  font-size: calc(1.2rem + 1vw); /* 严格的等比例缩放 */
  font-weight: 300;
  margin-bottom: 10px;
  animation: fadeInUp 0.5s ease-in-out;
  color: #ffffff;
  transition: font-size 0.3s ease;
}

.section-inner-top p {
  font-size: clamp(1.1rem, calc(1.1rem + 0.5vw), 1.8rem);
  animation: fadeInUp 0.5s ease-in-out 0.2s;
  animation-fill-mode: both;
  font-weight: 300;
  line-height: 1.6;
  transition: font-size 0.3s ease;
  margin: 0.5rem 0;
}

/*卫星模拟攻防系统*/
.section-inner-left {
  position: absolute;
  top: 85%;
  left: 45%; /* 适当右移 */
  transform: translate(-50%, -50%);
  text-transform: uppercase;
  text-align: left;
  width: 80%;
}

.section-inner-left h5 {
  font-size: 65px;
  margin-bottom: 10px;
  animation: fadeInUp 0.5s ease-in-out;
}

.section-inner-left h3 {
  font-size: calc(1.2rem + 1vw); /* 严格的等比例缩放 */
  font-weight: 300;
  margin-bottom: 10px;
  animation: fadeInUp 0.5s ease-in-out;
  color: #f36f04fb;
  transition: font-size 0.3s ease;
}

.section-inner-left p {
  font-size: clamp(1.1rem, calc(1.1rem + 0.5vw), 1.8rem);
  animation: fadeInUp 0.5s ease-in-out 0.2s;
  animation-fill-mode: both;
  font-weight: 300;
  line-height: 1.6;
  transition: font-size 0.3s ease;
  margin: 0.5rem 0;
}

.section-with-image {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  font-size: clamp(1.1rem, calc(1.1rem + 0.5vw), 1.8rem);
  line-height: 1.6;
}

@media (max-width: 600px) {
  .weixin-image {
    width: 60%;
    max-width: 180px;
    height: auto;
    margin: 0 auto;
  }
  
  .section-with-image {
    flex-direction: column;
    width: 90%;
    padding-top: 2rem;
  }
  
  .vertical-divider {
    height: 2px;
    width: 80%;
    margin: 1rem auto;
  }

  /* 缩小移动端按钮 */
  .btn {
    min-width: 100px;
    padding: 10px 30px;
    font-size: 0.9rem;
  }
}
  width: 80%;
}

.section-inner-center {
  text-transform: uppercase;
  text-align: center;
  margin-right: 40px;
}

.weixin-image {
  width: 250px;
  height: auto;
  border-radius: 8px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  margin: 0 50px;
  transition: all 0.3s ease;
}

.vertical-divider {
  height: 500px;
  width: 2px;
  background-color: #fff;
  margin: 0 80px; /* 增加左右边距至80px */
  display: block;
}

.logo-image {
  width: 600px;
  height: auto;
  border-radius: 8px;
  box-shadow: none; /* 移除阴影效果 */
}

.section-inner-center h3 {
  font-size: calc(1.2rem + 1vw); /* 严格的等比例缩放 */
  margin-bottom: 10px;
  animation: fadeInUp 0.5s ease-in-out;
  font-weight: 300;
  transition: font-size 0.3s ease;
}

.section-inner-center h5 {
  font-size: min(calc(1.2rem + 1vw), 2.2rem); /* 调小比例 */
  margin-bottom: 10px;
  animation: fadeInUp 0.5s ease-in-out;
}

.section-inner-center p {
  font-size: clamp(1.1rem, calc(1.1rem + 0.5vw), 1.8rem);
  animation: fadeInUp 0.5s ease-in-out 0.2s;
  animation-fill-mode: both;
  font-weight: 300;
  line-height: 1.6;
  transition: font-size 0.3s ease;
  margin: 0.5rem 0;
}

.section-inner-center p[style*="color"] {
  font-size: clamp(1.1rem, calc(1.1rem + 0.4vw), 1.7rem);
  margin: 0.5rem 0;
  transition: font-size 0.3s ease;
}

.section-inner-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-transform: uppercase;
  text-align: center;
  width: 90%;
  max-width: 1400px; /* 进一步增加最大宽度 */
  padding: 0 5%;
}

.section-inner-right {
  position: absolute;
  top: 70%;
  right: 5%;
  transform: translateY(-50%);
  text-transform: uppercase;
  text-align: left;
  width: 40%;
}

.section-inner-right h3 {
  font-size: calc(1.2rem + 1vw); /* 严格的等比例缩放 */
  font-weight: 300;
  margin-bottom: 10px;
  animation: fadeInUp 0.5s ease-in-out;
  color: #f36f04fb;
  transition: font-size 0.3s ease;
}

.section-inner-right p {
  font-size: clamp(1rem, calc(1rem + 0.4vw), 1.6rem); /* 缩小缩放比例 */
  animation: fadeInUp 0.5s ease-in-out 0.2s;
  animation-fill-mode: both;
  font-weight: 300;
  line-height: 1.6;
  transition: font-size 0.3s ease;
  margin: 0.5rem 0;
}

/* Stats */
.stats {
  display: flex;
  flex-wrap: nowrap;
  width: 100%;
  margin: 0 auto;
  padding: 0;
  text-align: center;
  text-transform: uppercase;
  justify-content: center;
  gap: 80px;
}

@media (max-width: 768px) {
  .stats {
    flex-direction: column;
    align-items: center; /* 新增居中 */
    gap: 30px;
    padding: 0 20px;
  }
  .stats div {
    min-width: 100%;
    max-width: 320px; /* 限制最大宽度 */
    padding: 0;
    text-align: center; /* 确保文本居中 */
  }
}

.stats div {
  flex: 0 0 auto;
  min-width: 300px;
  padding: 0 40px;
}

.stats div span {
  font-size: clamp(1.8rem, calc(1.8rem + 0.8vw), 2.8rem);
  color: #ffffff;
  transition: font-size 0.3s ease;
}

.stats div p {
  font-size: clamp(0.8rem, calc(0.8rem + 0.4vw), 1.3rem);
  margin-top: 0.5rem;
  font-weight: 300;
  transition: font-size 0.3s ease;
  color: inherit;
}

.stats div h4 {
  font-size: min(calc(0.9rem + 0.6vw), 1.5rem);
  font-weight: 300;
}

@media (max-width: 600px) {
  .stats div span {
    font-size: 1.8rem;
  }
  .stats div p {
    font-size: 0.9rem;
  }
  .stats div h4 {
    font-size: 0.9rem;
  }
  .section-inner-center p[style*="color"] {
    font-size: 1.1rem;
  }
}

/* Animations */

@keyframes fadeInUp {
  0% {
    opacity: 0;
    transform: translateY(140px);
  }

  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  0% {
    opacity: 0;
  }

  100% {
    opacity: 1;
  }
}

@keyframes fadeBounce {
  0%,
  20%,
  50%,
  80%,
  100% {
    opacity: 0;
    transform: translateY(-20px);
  }

  40% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 基础响应式单位 - 等比例缩放 */
:root {
  font-size: calc(16px + 0.4vw);
  --min-font: 16px;
  --max-font: 22px;
  transition: font-size 0.3s ease-out;
}

/* 响应式断点 */
@media (max-width: 1440px) {
  :root {
    font-size: calc(var(--base-font-size) * var(--laptop-scale));
  }
  .main-header {
    padding: 0 2rem;
  }
  .desktop-main-menu {
    margin-right: 2rem;
  }
  
  /* 笔记本专用调整 */
  .section-inner {
    max-width: 70vw;
  }
  .logo {
    width: clamp(160px, 12vw, 220px);
  }
}

@media (max-width: 960px) {
  .desktop-main-menu {
    display: none;
  }
  .mobile-only {
    display: block;
  }
  .section-inner-center h3 {
    font-size: 5rem;
  }
  .logo {
    width: 18rem;
  }
}

@media (max-width: 768px) {
  .main-header {
    height: 8rem;
  }
  .section-inner {
    left: 5vw;
    bottom: 15vh;
    max-width: 90vw;
  }
  .section-inner h2 {
    font-size: clamp(28px, calc(3rem + 1.5vw), 48px);
  }
  .section-inner h4 {
    font-size: clamp(16px, calc(1.2rem + 1vw), 22px);
  }
}

@media (max-width: 600px) {
  :root {
    font-size: 14px;
  }
  .section-inner {
    left: 5%;
    bottom: 10vh;
  }
  .section-inner h2 {
    font-size: clamp(24px, calc(2rem + 1vw), 32px); /* 移动端也保持等比例缩放 */
  }
  .section-inner h3 {
    font-size: 1.8rem;
  }
  .section-inner h4 {
    font-size: 1.2rem;
  }
  .section-inner p {
    font-size: 1rem;
  }
  footer ul li {
    font-size: 1rem;
    margin-right: 1rem;
  }
  .logo {
    width: 15rem;
    max-width: 100%;
    height: auto;
  }
  
  /* 统一其他页面移动端字体 */
  .section-inner-top h3,
  .section-inner-center h3,
  .section-inner-right h3 {
    font-size: 1.8rem;
  }
  .section-inner-top p,
  .section-inner-center p,
  .section-inner-right p {
    font-size: 1rem;
  }

  /* focuson.html 图片响应式处理 */
  .weixin-image {
    width: 80%;
    max-width: 250px;
    height: auto;
    margin: 0 auto;
  }
  
  .section-with-image {
    flex-direction: column;
    width: 90%;
  }
  
  .vertical-divider {
    height: 2px;
    width: 80%;
    margin: 20px auto;
  }
}

@media (max-width: 480px) {
  .main-header {
    padding: 0 1rem;
    height: 7rem;
  }
  .mobile-main-menu {
    width: 100%;
  }
  .section-inner h2 {
    font-size: 2.5rem;
  }
}
