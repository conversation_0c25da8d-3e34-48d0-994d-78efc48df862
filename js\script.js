const btn = document.getElementById('menu-btn');
const overlay = document.getElementById('overlay');
const menu = document.getElementById('mobile-menu');
const counters = document.querySelectorAll('.counter');

btn.addEventListener('click', navToggle);

// 页面加载后等待1秒激活动画
window.addEventListener('DOMContentLoaded', () => {
  // 初始化产品子菜单状态
  document.querySelectorAll('.products-submenu').forEach(submenu => {
    submenu.style.display = 'none';
  });

  // 添加产品菜单点击事件
  document.querySelectorAll('.has-submenu > a').forEach(item => {
    item.addEventListener('click', function(e) {
      e.preventDefault();
      const submenu = this.nextElementSibling;
      submenu.style.display = submenu.style.display === 'block' ? 'none' : 'block';
    });
  });

  // Logo保护功能
  const logoImages = document.querySelectorAll('.logo img, .logo-image');
  logoImages.forEach(img => {
    // 禁止右键菜单
    img.addEventListener('contextmenu', function(e) {
      e.preventDefault();
      return false;
    });

    // 禁止拖拽
    img.addEventListener('dragstart', function(e) {
      e.preventDefault();
      return false;
    });

    // 禁止选择
    img.addEventListener('selectstart', function(e) {
      e.preventDefault();
      return false;
    });
  });

  // 禁用开发者工具和保存快捷键
  document.addEventListener('keydown', function(e) {
    // 禁用F12开发者工具
    if (e.key === 'F12') {
      e.preventDefault();
      return false;
    }

    // 禁用Ctrl+S保存
    if (e.ctrlKey && e.key === 's') {
      e.preventDefault();
      return false;
    }

    // 禁用Ctrl+Shift+I开发者工具
    if (e.ctrlKey && e.shiftKey && e.key === 'I') {
      e.preventDefault();
      return false;
    }

    // 禁用Ctrl+U查看源代码
    if (e.ctrlKey && e.key === 'u') {
      e.preventDefault();
      return false;
    }
  });

  setTimeout(() => {
    counters.forEach((counter) => {
      counter.innerText = '0';

      const updateCounter = () => {
        // Get count target
        const target = +counter.getAttribute('data-target');
        // Get current counter value
        const c = +counter.innerText;

        // Create an increment
        const increment = target / 50;

        // If counter is less than target, add increment
        if (c < target) {
          // Round up and set counter value
          counter.innerText = `${Math.ceil(c + increment)}`;

          setTimeout(updateCounter, 75);
        } else {
          counter.innerText = target;
        }
      };

      updateCounter();
    });
  }, 500);
});

function navToggle() {
  btn.classList.toggle('open');
  overlay.classList.toggle('overlay-show');
  document.body.classList.toggle('stop-scrolling');
  menu.classList.toggle('show-menu');
}
