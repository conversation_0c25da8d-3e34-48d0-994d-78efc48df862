const btn = document.getElementById('menu-btn');
const overlay = document.getElementById('overlay');
const menu = document.getElementById('mobile-menu');
const counters = document.querySelectorAll('.counter');

btn.addEventListener('click', navToggle);

// 页面加载后等待1秒激活动画
window.addEventListener('DOMContentLoaded', () => {
  // 初始化产品子菜单状态
  document.querySelectorAll('.products-submenu').forEach(submenu => {
    submenu.style.display = 'none';
  });

  // 添加产品菜单点击事件
  document.querySelectorAll('.has-submenu > a').forEach(item => {
    item.addEventListener('click', function(e) {
      e.preventDefault();
      const submenu = this.nextElementSibling;
      submenu.style.display = submenu.style.display === 'block' ? 'none' : 'block';
    });
  });

  // 增强Logo保护功能
  function protectLogo() {
    const logoImages = document.querySelectorAll('.logo img, .logo-image');

    logoImages.forEach(img => {
      // 多重事件监听器
      const events = ['contextmenu', 'dragstart', 'selectstart', 'mousedown', 'touchstart'];
      events.forEach(event => {
        img.addEventListener(event, function(e) {
          e.preventDefault();
          e.stopPropagation();
          return false;
        }, true);
      });

      // 动态替换为背景图片
      const parent = img.parentElement;
      const imgSrc = img.src;
      const imgAlt = img.alt;

      // 创建div替代img标签
      const logoDiv = document.createElement('div');
      logoDiv.style.backgroundImage = `url(${imgSrc})`;
      logoDiv.style.backgroundSize = 'contain';
      logoDiv.style.backgroundRepeat = 'no-repeat';
      logoDiv.style.backgroundPosition = 'center';
      logoDiv.style.width = img.offsetWidth + 'px';
      logoDiv.style.height = img.offsetHeight + 'px';
      logoDiv.style.display = 'block';
      logoDiv.setAttribute('aria-label', imgAlt);
      logoDiv.className = img.className;

      // 添加保护样式
      logoDiv.style.userSelect = 'none';
      logoDiv.style.webkitUserSelect = 'none';
      logoDiv.style.mozUserSelect = 'none';
      logoDiv.style.msUserSelect = 'none';
      logoDiv.style.pointerEvents = 'auto';

      // 替换原img标签
      parent.replaceChild(logoDiv, img);

      // 为新div添加事件监听
      events.forEach(event => {
        logoDiv.addEventListener(event, function(e) {
          e.preventDefault();
          e.stopPropagation();
          return false;
        }, true);
      });
    });
  }

  // 延迟执行保护
  setTimeout(protectLogo, 100);

  // 全局保护措施
  document.addEventListener('keydown', function(e) {
    // 禁用各种开发者工具快捷键
    const forbiddenKeys = [
      'F12',
      { ctrl: true, shift: true, key: 'I' },
      { ctrl: true, shift: true, key: 'J' },
      { ctrl: true, shift: true, key: 'C' },
      { ctrl: true, key: 'u' },
      { ctrl: true, key: 's' },
      { ctrl: true, key: 'a' },
      { ctrl: true, key: 'p' }
    ];

    for (let forbidden of forbiddenKeys) {
      if (typeof forbidden === 'string') {
        if (e.key === forbidden) {
          e.preventDefault();
          return false;
        }
      } else {
        if ((!forbidden.ctrl || e.ctrlKey) &&
            (!forbidden.shift || e.shiftKey) &&
            (!forbidden.alt || e.altKey) &&
            e.key.toLowerCase() === forbidden.key.toLowerCase()) {
          e.preventDefault();
          return false;
        }
      }
    }
  });

  // 禁用右键菜单（全局）
  document.addEventListener('contextmenu', function(e) {
    const target = e.target;
    if (target.closest('.logo') || target.classList.contains('logo-image')) {
      e.preventDefault();
      return false;
    }
  });

  // 检测开发者工具
  let devtools = {open: false, orientation: null};
  const threshold = 160;

  setInterval(() => {
    if (window.outerHeight - window.innerHeight > threshold ||
        window.outerWidth - window.innerWidth > threshold) {
      if (!devtools.open) {
        devtools.open = true;
        // 可以在这里添加警告或其他措施
        console.clear();
      }
    } else {
      devtools.open = false;
    }
  }, 500);

  setTimeout(() => {
    counters.forEach((counter) => {
      counter.innerText = '0';

      const updateCounter = () => {
        // Get count target
        const target = +counter.getAttribute('data-target');
        // Get current counter value
        const c = +counter.innerText;

        // Create an increment
        const increment = target / 50;

        // If counter is less than target, add increment
        if (c < target) {
          // Round up and set counter value
          counter.innerText = `${Math.ceil(c + increment)}`;

          setTimeout(updateCounter, 75);
        } else {
          counter.innerText = target;
        }
      };

      updateCounter();
    });
  }, 500);
});

function navToggle() {
  btn.classList.toggle('open');
  overlay.classList.toggle('overlay-show');
  document.body.classList.toggle('stop-scrolling');
  menu.classList.toggle('show-menu');
}
