const btn = document.getElementById('menu-btn');
const overlay = document.getElementById('overlay');
const menu = document.getElementById('mobile-menu');
const counters = document.querySelectorAll('.counter');

btn.addEventListener('click', navToggle);

// 页面加载后等待1秒激活动画
window.addEventListener('DOMContentLoaded', () => {
  // 初始化产品子菜单状态
  document.querySelectorAll('.products-submenu').forEach(submenu => {
    submenu.style.display = 'none';
  });

  // 添加产品菜单点击事件
  document.querySelectorAll('.has-submenu > a').forEach(item => {
    item.addEventListener('click', function(e) {
      e.preventDefault();
      const submenu = this.nextElementSibling;
      submenu.style.display = submenu.style.display === 'block' ? 'none' : 'block';
    });
  });

  setTimeout(() => {
    counters.forEach((counter) => {
      counter.innerText = '0';

      const updateCounter = () => {
        // Get count target
        const target = +counter.getAttribute('data-target');
        // Get current counter value
        const c = +counter.innerText;

        // Create an increment
        const increment = target / 50;

        // If counter is less than target, add increment
        if (c < target) {
          // Round up and set counter value
          counter.innerText = `${Math.ceil(c + increment)}`;

          setTimeout(updateCounter, 75);
        } else {
          counter.innerText = target;
        }
      };

      updateCounter();
    });
  }, 500);
});

function navToggle() {
  btn.classList.toggle('open');
  overlay.classList.toggle('overlay-show');
  document.body.classList.toggle('stop-scrolling');
  menu.classList.toggle('show-menu');
}
