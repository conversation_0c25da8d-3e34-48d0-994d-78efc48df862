// 高级Logo保护系统
(function() {
    'use strict';
    
    // 反调试措施
    function antiDebug() {
        // 检测开发者工具
        let devtools = false;
        const threshold = 160;
        
        function detectDevTools() {
            if (window.outerHeight - window.innerHeight > threshold || 
                window.outerWidth - window.innerWidth > threshold) {
                if (!devtools) {
                    devtools = true;
                    // 清空控制台
                    console.clear();
                    // 可以添加更多反制措施
                }
            } else {
                devtools = false;
            }
        }
        
        setInterval(detectDevTools, 100);
        
        // 禁用控制台
        Object.defineProperty(window, 'console', {
            value: {
                log: function() {},
                warn: function() {},
                error: function() {},
                info: function() {},
                debug: function() {},
                clear: function() {}
            },
            writable: false,
            configurable: false
        });
    }
    
    // Logo保护核心函数
    function protectLogos() {
        const logoSelectors = ['.logo img', '.logo-image', 'img[src*="logo"]'];
        
        logoSelectors.forEach(selector => {
            const elements = document.querySelectorAll(selector);
            elements.forEach(element => {
                protectElement(element);
            });
        });
    }
    
    function protectElement(element) {
        if (!element) return;
        
        // 获取原始信息
        const src = element.src;
        const alt = element.alt || '';
        const className = element.className;
        const parent = element.parentElement;
        
        // 创建保护容器
        const protectedDiv = document.createElement('div');
        protectedDiv.className = className;
        protectedDiv.setAttribute('data-protected', 'true');
        protectedDiv.setAttribute('aria-label', alt);
        
        // 复制样式
        const computedStyle = window.getComputedStyle(element);
        protectedDiv.style.width = computedStyle.width;
        protectedDiv.style.height = computedStyle.height;
        protectedDiv.style.display = computedStyle.display;
        protectedDiv.style.backgroundImage = `url(${src})`;
        protectedDiv.style.backgroundSize = 'contain';
        protectedDiv.style.backgroundRepeat = 'no-repeat';
        protectedDiv.style.backgroundPosition = 'center';
        
        // 添加保护样式
        protectedDiv.style.userSelect = 'none';
        protectedDiv.style.webkitUserSelect = 'none';
        protectedDiv.style.mozUserSelect = 'none';
        protectedDiv.style.msUserSelect = 'none';
        protectedDiv.style.webkitUserDrag = 'none';
        protectedDiv.style.mozUserDrag = 'none';
        protectedDiv.style.userDrag = 'none';
        
        // 添加事件监听器
        const protectedEvents = [
            'contextmenu', 'dragstart', 'selectstart', 'mousedown', 
            'touchstart', 'copy', 'cut', 'paste', 'beforecopy'
        ];
        
        protectedEvents.forEach(eventType => {
            protectedDiv.addEventListener(eventType, function(e) {
                e.preventDefault();
                e.stopPropagation();
                e.stopImmediatePropagation();
                return false;
            }, true);
        });
        
        // 替换原元素
        parent.replaceChild(protectedDiv, element);
        
        // 添加额外保护层
        addProtectionLayer(protectedDiv);
    }
    
    function addProtectionLayer(element) {
        // 创建透明保护层
        const overlay = document.createElement('div');
        overlay.style.position = 'absolute';
        overlay.style.top = '0';
        overlay.style.left = '0';
        overlay.style.width = '100%';
        overlay.style.height = '100%';
        overlay.style.zIndex = '999';
        overlay.style.backgroundColor = 'transparent';
        overlay.style.pointerEvents = 'auto';
        
        // 设置父元素为相对定位
        if (element.parentElement) {
            element.parentElement.style.position = 'relative';
            element.parentElement.appendChild(overlay);
        }
        
        // 为保护层添加事件监听
        const events = ['contextmenu', 'dragstart', 'selectstart', 'mousedown', 'touchstart'];
        events.forEach(eventType => {
            overlay.addEventListener(eventType, function(e) {
                e.preventDefault();
                e.stopPropagation();
                return false;
            }, true);
        });
    }
    
    // 全局键盘保护
    function setupKeyboardProtection() {
        document.addEventListener('keydown', function(e) {
            // 禁用的键盘组合
            const forbidden = [
                // 开发者工具
                {key: 'F12'},
                {ctrl: true, shift: true, key: 'I'},
                {ctrl: true, shift: true, key: 'J'},
                {ctrl: true, shift: true, key: 'C'},
                {ctrl: true, key: 'U'},
                // 保存和打印
                {ctrl: true, key: 'S'},
                {ctrl: true, key: 'P'},
                // 选择和复制
                {ctrl: true, key: 'A'},
                {ctrl: true, key: 'C'},
                {ctrl: true, key: 'V'},
                {ctrl: true, key: 'X'}
            ];
            
            for (let combo of forbidden) {
                if (matchesKeyCombo(e, combo)) {
                    e.preventDefault();
                    e.stopPropagation();
                    return false;
                }
            }
        }, true);
    }
    
    function matchesKeyCombo(event, combo) {
        return (!combo.ctrl || event.ctrlKey) &&
               (!combo.shift || event.shiftKey) &&
               (!combo.alt || event.altKey) &&
               (!combo.meta || event.metaKey) &&
               event.key.toLowerCase() === combo.key.toLowerCase();
    }
    
    // 初始化保护系统
    function init() {
        // 等待DOM加载完成
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', function() {
                setTimeout(function() {
                    antiDebug();
                    protectLogos();
                    setupKeyboardProtection();
                }, 100);
            });
        } else {
            setTimeout(function() {
                antiDebug();
                protectLogos();
                setupKeyboardProtection();
            }, 100);
        }
        
        // 监听动态添加的元素
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                mutation.addedNodes.forEach(function(node) {
                    if (node.nodeType === 1) { // Element node
                        const logos = node.querySelectorAll('.logo img, .logo-image, img[src*="logo"]');
                        logos.forEach(protectElement);
                    }
                });
            });
        });
        
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    }
    
    // 启动保护系统
    init();
    
})();
