# Fix logo transparency by re-converting PNG files properly
Add-Type -AssemblyName System.Drawing

function Convert-PngToWebP {
    param(
        [string]$InputPath,
        [string]$OutputPath
    )
    
    try {
        $image = [System.Drawing.Image]::FromFile($InputPath)
        
        # Create a new bitmap with proper transparency support
        $bitmap = New-Object System.Drawing.Bitmap($image.Width, $image.Height, [System.Drawing.Imaging.PixelFormat]::Format32bppArgb)
        $graphics = [System.Drawing.Graphics]::FromImage($bitmap)
        
        # Set high quality rendering
        $graphics.CompositingQuality = [System.Drawing.Drawing2D.CompositingQuality]::HighQuality
        $graphics.InterpolationMode = [System.Drawing.Drawing2D.InterpolationMode]::HighQualityBicubic
        
        # Draw the original image (preserving transparency)
        $graphics.DrawImage($image, 0, 0, $image.Width, $image.Height)
        $graphics.Dispose()
        
        # Save as PNG with .webp extension (to maintain transparency)
        $pngCodec = [System.Drawing.Imaging.ImageCodecInfo]::GetImageEncoders() | Where-Object { $_.MimeType -eq "image/png" }
        $bitmap.Save($OutputPath, $pngCodec)
        
        $bitmap.Dispose()
        $image.Dispose()
        
        Write-Host "Converted: $InputPath -> $OutputPath" -ForegroundColor Green
        return $true
    }
    catch {
        Write-Error "Failed to convert $InputPath : $($_.Exception.Message)"
        return $false
    }
}

# Re-convert PNG files to maintain transparency
$pngFiles = @("logo.png", "logo_big.png", "SpaceDefense.png")

Write-Host "Re-converting PNG files to maintain transparency..." -ForegroundColor Cyan

foreach ($fileName in $pngFiles) {
    $inputPath = "img\$fileName"
    $outputPath = "img\$($fileName -replace '\.png$', '.webp')"
    
    if (Test-Path $inputPath) {
        Write-Host "Processing: $fileName" -ForegroundColor Yellow
        
        # Remove existing WebP file
        if (Test-Path $outputPath) {
            Remove-Item $outputPath -Force
        }
        
        # Convert with transparency
        Convert-PngToWebP -InputPath $inputPath -OutputPath $outputPath
    }
}

Write-Host "Conversion completed!" -ForegroundColor Cyan
