# 批量更新HTML文件，添加logo保护脚本
$htmlFiles = Get-ChildItem -Path "." -Filter "*.html" | Where-Object { $_.Name -ne "index.html" }

Write-Host "正在更新HTML文件以添加logo保护..." -ForegroundColor Cyan

foreach ($file in $htmlFiles) {
    Write-Host "处理文件: $($file.Name)" -ForegroundColor Yellow
    
    $content = Get-Content $file.FullName -Raw -Encoding UTF8
    
    # 检查是否已经包含logo-protection.js
    if ($content -notmatch 'logo-protection\.js') {
        # 查找script.js的位置并在其前面添加logo-protection.js
        if ($content -match '<script src="js/script\.js" defer></script>') {
            $newContent = $content -replace '<script src="js/script\.js" defer></script>', '<script src="logo-protection.js"></script>`n    <script src="js/script.js" defer></script>'
            Set-Content -Path $file.FullName -Value $newContent -Encoding UTF8
            Write-Host "✓ 已更新: $($file.Name)" -ForegroundColor Green
        } else {
            Write-Host "⚠ 未找到script.js引用: $($file.Name)" -ForegroundColor Yellow
        }
    } else {
        Write-Host "- 已包含保护脚本: $($file.Name)" -ForegroundColor Gray
    }
}

Write-Host "`n更新完成！" -ForegroundColor Cyan
