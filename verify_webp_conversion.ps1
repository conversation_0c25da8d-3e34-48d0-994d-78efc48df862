# Verification script for WebP conversion
Write-Host "=== WebP 转换验证报告 ===" -ForegroundColor Cyan

# 1. 检查 WebP 文件是否存在
Write-Host "`n1. 检查 WebP 文件..." -ForegroundColor Yellow
$webpFiles = Get-ChildItem -Path "img" -Filter "*.webp" | Sort-Object Name
Write-Host "找到 $($webpFiles.Count) 个 WebP 文件:" -ForegroundColor Green
$webpFiles | ForEach-Object { Write-Host "  - $($_.Name)" -ForegroundColor White }

# 2. 检查原始文件是否仍然存在
Write-Host "`n2. 检查原始图片文件..." -ForegroundColor Yellow
$originalFiles = Get-ChildItem -Path "img" -Include "*.jpg", "*.jpeg", "*.png" -Recurse | Sort-Object Name
Write-Host "仍存在 $($originalFiles.Count) 个原始图片文件:" -ForegroundColor Green
$originalFiles | ForEach-Object { Write-Host "  - $($_.Name)" -ForegroundColor White }

# 3. 检查代码中是否还有旧的图片引用
Write-Host "`n3. 检查代码中的图片引用..." -ForegroundColor Yellow
$oldReferences = Select-String -Path "*.html", "css\*.css" -Pattern "img/.*\.(jpg|png)" -AllMatches
if ($oldReferences) {
    Write-Host "发现 $($oldReferences.Count) 个未更新的引用:" -ForegroundColor Red
    $oldReferences | ForEach-Object { Write-Host "  - $($_.Filename):$($_.LineNumber) - $($_.Line.Trim())" -ForegroundColor Red }
} else {
    Write-Host "✓ 所有图片引用已成功更新为 WebP 格式" -ForegroundColor Green
}

# 4. 检查 WebP 文件大小对比
Write-Host "`n4. 文件大小对比..." -ForegroundColor Yellow
$totalOriginalSize = 0
$totalWebpSize = 0

foreach ($webpFile in $webpFiles) {
    $webpPath = $webpFile.FullName
    $webpSize = $webpFile.Length
    $totalWebpSize += $webpSize
    
    # 查找对应的原始文件
    $baseName = [System.IO.Path]::GetFileNameWithoutExtension($webpFile.Name)
    $originalJpg = Get-ChildItem -Path "img" -Filter "$baseName.jpg" -ErrorAction SilentlyContinue
    $originalPng = Get-ChildItem -Path "img" -Filter "$baseName.png" -ErrorAction SilentlyContinue
    
    $originalFile = if ($originalJpg) { $originalJpg } else { $originalPng }
    if ($originalFile) {
        $originalSize = $originalFile.Length
        $totalOriginalSize += $originalSize
        $savings = [math]::Round((($originalSize - $webpSize) / $originalSize) * 100, 1)
        $webpSizeKB = [math]::Round($webpSize / 1KB, 1)
        $originalSizeKB = [math]::Round($originalSize / 1KB, 1)
        
        Write-Host "  $baseName: $originalSizeKB KB → $webpSizeKB KB (节省 $savings%)" -ForegroundColor White
    }
}

if ($totalOriginalSize -gt 0) {
    $totalSavings = [math]::Round((($totalOriginalSize - $totalWebpSize) / $totalOriginalSize) * 100, 1)
    $totalOriginalMB = [math]::Round($totalOriginalSize / 1MB, 2)
    $totalWebpMB = [math]::Round($totalWebpSize / 1MB, 2)
    
    Write-Host "`n总计: $totalOriginalMB MB → $totalWebpMB MB (节省 $totalSavings%)" -ForegroundColor Cyan
}

Write-Host "`n=== 验证完成 ===" -ForegroundColor Cyan
Write-Host "建议: 在确认网站正常运行后，可以删除原始的 JPG 和 PNG 文件以节省空间。" -ForegroundColor Yellow
