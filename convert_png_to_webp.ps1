# PowerShell script to properly convert PNG images to WebP with transparency support
# This script uses .NET System.Drawing to convert images while preserving transparency

Add-Type -AssemblyName System.Drawing

function Convert-PngToWebP {
    param(
        [string]$InputPath,
        [string]$OutputPath,
        [int]$Quality = 90
    )
    
    try {
        # Load the original PNG image
        $image = [System.Drawing.Image]::FromFile($InputPath)
        
        # Check if the image has transparency
        $hasTransparency = $false
        if ($image.PixelFormat -eq [System.Drawing.Imaging.PixelFormat]::Format32bppArgb -or
            $image.PixelFormat -eq [System.Drawing.Imaging.PixelFormat]::Format8bppIndexed) {
            $hasTransparency = $true
        }
        
        Write-Host "Processing: $([System.IO.Path]::GetFileName($InputPath)) - Transparency: $hasTransparency" -ForegroundColor Blue
        
        # Create a new bitmap to ensure proper format
        $bitmap = New-Object System.Drawing.Bitmap($image.Width, $image.Height, [System.Drawing.Imaging.PixelFormat]::Format32bppArgb)
        $graphics = [System.Drawing.Graphics]::FromImage($bitmap)
        
        # Set high quality rendering
        $graphics.CompositingQuality = [System.Drawing.Drawing2D.CompositingQuality]::HighQuality
        $graphics.InterpolationMode = [System.Drawing.Drawing2D.InterpolationMode]::HighQualityBicubic
        $graphics.SmoothingMode = [System.Drawing.Drawing2D.SmoothingMode]::HighQuality
        
        # If the image has transparency, don't fill background
        if (-not $hasTransparency) {
            $graphics.Clear([System.Drawing.Color]::White)
        }
        
        # Draw the original image onto the new bitmap
        $graphics.DrawImage($image, 0, 0, $image.Width, $image.Height)
        $graphics.Dispose()
        
        # Create encoder parameters for high quality
        $encoderParams = New-Object System.Drawing.Imaging.EncoderParameters(1)
        $encoderParams.Param[0] = New-Object System.Drawing.Imaging.EncoderParameter([System.Drawing.Imaging.Encoder]::Quality, $Quality)
        
        # Try to get WebP codec, fallback to PNG if not available
        $webpCodec = [System.Drawing.Imaging.ImageCodecInfo]::GetImageEncoders() | Where-Object { $_.MimeType -eq "image/webp" }
        
        if ($webpCodec) {
            # Save as WebP
            $bitmap.Save($OutputPath, $webpCodec, $encoderParams)
            Write-Host "Converted to WebP: $OutputPath" -ForegroundColor Green
        } else {
            # Fallback: Save as PNG (which supports transparency)
            $pngCodec = [System.Drawing.Imaging.ImageCodecInfo]::GetImageEncoders() | Where-Object { $_.MimeType -eq "image/png" }
            $tempPng = $OutputPath -replace "\.webp$", ".temp.png"
            $bitmap.Save($tempPng, $pngCodec)
            Move-Item $tempPng $OutputPath -Force
            Write-Host "Converted to PNG as WebP: $OutputPath" -ForegroundColor Yellow
        }
        
        $bitmap.Dispose()
        $image.Dispose()
        return $true
    }
    catch {
        Write-Error "Failed to convert $InputPath : $($_.Exception.Message)"
        if ($bitmap) { $bitmap.Dispose() }
        if ($graphics) { $graphics.Dispose() }
        if ($image) { $image.Dispose() }
        return $false
    }
}

# Convert specific PNG files that need transparency
$pngFiles = @("logo.png", "logo_big.png", "SpaceDefense.png")

Write-Host "重新转换PNG文件以保持透明度..." -ForegroundColor Cyan

foreach ($fileName in $pngFiles) {
    $inputPath = "img\$fileName"
    $outputPath = "img\$($fileName -replace '\.png$', '.webp')"
    
    if (Test-Path $inputPath) {
        Write-Host "`n处理文件: $fileName" -ForegroundColor Yellow
        
        # Remove existing WebP file if it exists
        if (Test-Path $outputPath) {
            Remove-Item $outputPath -Force
            Write-Host "删除现有的WebP文件" -ForegroundColor Gray
        }
        
        # Convert with transparency support
        if (Convert-PngToWebP -InputPath $inputPath -OutputPath $outputPath -Quality 95) {
            Write-Host "✓ 成功转换: $fileName" -ForegroundColor Green
        } else {
            Write-Host "✗ 转换失败: $fileName" -ForegroundColor Red
        }
    } else {
        Write-Host "文件不存在: $inputPath" -ForegroundColor Red
    }
}

Write-Host "`n转换完成！请检查logo是否保持透明背景。" -ForegroundColor Cyan
